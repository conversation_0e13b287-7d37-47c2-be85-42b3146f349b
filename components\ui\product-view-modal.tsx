"use client"

import { useState } from "react"
import Image from "next/image"
import { motion, AnimatePresence } from "framer-motion"
import { X, ShoppingCart, Download, Star, Calendar, Package, Smartphone, Monitor, Shield, Zap } from "lucide-react"
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { useLanguage } from "@/components/providers/language-provider"
import { Hack } from "@/types/hack"
import { Account } from "@/types/account"

interface ProductViewModalProps {
  isOpen: boolean
  onClose: () => void
  product: Hack | Account | null
  productType: 'hack' | 'account'
}

export function ProductViewModal({ isOpen, onClose, product, productType }: ProductViewModalProps) {
  const { language } = useLanguage()

  if (!product) return null

  const isHack = productType === 'hack'
  const hack = isHack ? product as Hack : null
  const account = !isHack ? product as Account : null

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500'
      case 'inactive': return 'bg-gray-500'
      case 'maintenance': return 'bg-yellow-500'
      case 'sold': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  const getPlatformIcon = (platform?: string) => {
    switch (platform) {
      case 'android': return <Smartphone className="w-4 h-4" />
      case 'ios': return <Smartphone className="w-4 h-4" />
      case 'pc': return <Monitor className="w-4 h-4" />
      default: return <Package className="w-4 h-4" />
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-background border-border">
        <DialogHeader className="space-y-4">
          <div className="flex items-start justify-between">
            <DialogTitle className="text-2xl font-bold text-foreground pr-8">
              {product.name[language]}
            </DialogTitle>
            {product.isFeatured && (
              <Badge className="bg-orange-500 text-white">
                <Star className="w-3 h-3 mr-1" />
                {language === "en" ? "Featured" : "مميز"}
              </Badge>
            )}
          </div>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Product Image */}
          <div className="space-y-4">
            <div className="relative overflow-hidden rounded-lg border border-border">
              <Image
                src={product.imageUrl || "/placeholder.svg"}
                alt={product.name[language]}
                width={500}
                height={400}
                className="w-full h-64 sm:h-80 object-cover"
              />
            </div>
          </div>

          {/* Product Details */}
          <div className="space-y-6">
            {/* Price and Status */}
            <div className="flex items-center justify-between">
              <div className="text-3xl font-bold text-orange-400">
                ${product.price}
              </div>
              <Badge className={`${getStatusColor(product.status)} text-white`}>
                {product.status}
              </Badge>
            </div>

            {/* Hack-specific details */}
            {isHack && hack && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="text-sm text-muted-foreground">
                      {language === "en" ? "Type" : "النوع"}
                    </div>
                    <div className="flex items-center space-x-2">
                      {getPlatformIcon(hack.type === 'pc' ? 'pc' : hack.platform)}
                      <span className="capitalize">{hack.type}</span>
                    </div>
                  </div>
                  
                  {hack.platform && (
                    <div className="space-y-2">
                      <div className="text-sm text-muted-foreground">
                        {language === "en" ? "Platform" : "المنصة"}
                      </div>
                      <div className="capitalize">{hack.platform}</div>
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="text-sm text-muted-foreground">
                      {language === "en" ? "Company" : "الشركة"}
                    </div>
                    <div>{hack.company}</div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="text-sm text-muted-foreground">
                      {language === "en" ? "Version" : "الإصدار"}
                    </div>
                    <div>{hack.version}</div>
                  </div>
                </div>

                {(hack.rootStatus || hack.jailbreakStatus) && (
                  <div className="space-y-2">
                    <div className="text-sm text-muted-foreground">
                      {language === "en" ? "Requirements" : "المتطلبات"}
                    </div>
                    <div className="flex items-center space-x-2">
                      <Shield className="w-4 h-4" />
                      <span className="capitalize">
                        {hack.rootStatus || hack.jailbreakStatus}
                      </span>
                    </div>
                  </div>
                )}

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="text-sm text-muted-foreground">
                      {language === "en" ? "Downloads" : "التحميلات"}
                    </div>
                    <div className="flex items-center space-x-2">
                      <Download className="w-4 h-4" />
                      <span>{hack.downloadCount.toLocaleString()}</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="text-sm text-muted-foreground">
                      {language === "en" ? "Last Updated" : "آخر تحديث"}
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-4 h-4" />
                      <span>{new Date(hack.lastUpdated).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Account-specific details */}
            {!isHack && account && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="text-sm text-muted-foreground">
                      {language === "en" ? "Created" : "تاريخ الإنشاء"}
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-4 h-4" />
                      <span>{new Date(account.createdAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="text-sm text-muted-foreground">
                      {language === "en" ? "Updated" : "آخر تحديث"}
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-4 h-4" />
                      <span>{new Date(account.updatedAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <Separator />

            {/* Description */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">
                {language === "en" ? "Description" : "الوصف"}
              </h3>
              <p className="text-muted-foreground leading-relaxed">
                {product.description[language]}
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <Button
                className="flex-1 bg-orange-500 hover:bg-orange-600 text-white"
                disabled={product.status !== "active"}
              >
                <ShoppingCart className="w-4 h-4 mr-2" />
                {language === "en" ? "Add to Cart" : "أضف للسلة"}
              </Button>
              
              {isHack && (
                <Button variant="outline" className="flex-1">
                  <Zap className="w-4 h-4 mr-2" />
                  {language === "en" ? "View Details" : "عرض التفاصيل"}
                </Button>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
