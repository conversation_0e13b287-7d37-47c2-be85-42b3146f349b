"use client"

import { createContext, useContext, useState, type ReactNode } from "react"

type Language = "en" | "ar"

interface LanguageContextType {
  language: Language
  setLanguage: (lang: Language) => void
  t: (key: string) => string
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

const translations = {
  en: {
    login: "Login",
    signup: "Sign Up",
    home: "Home",
    accounts: "Accounts",
    tools: "Tools",
    profile: "Profile",
    search: "Search...",
    featured: "Featured Products",
    viewAll: "View All",
    buyNow: "Buy Now",
    price: "Price",
    level: "Level",
    rank: "Rank",
    skins: "Skins",
    inStock: "In Stock",
    outOfStock: "Out of Stock",
    reviews: "Reviews",
    features: "Features",
  },
  ar: {
    login: "تسجيل الدخول",
    signup: "إنشاء حساب",
    home: "الرئيسية",
    accounts: "الحسابات",
    tools: "الأدوات",
    profile: "الملف الشخصي",
    search: "البحث...",
    featured: "المنتجات المميزة",
    viewAll: "عرض الكل",
    buyNow: "اشتري الآن",
    price: "السعر",
    level: "المستوى",
    rank: "الرتبة",
    skins: "الأشكال",
    inStock: "متوفر",
    outOfStock: "غير متوفر",
    reviews: "التقييمات",
    features: "المميزات",
  },
}

export function LanguageProvider({
  children,
  locale,
}: {
  children: ReactNode
  locale?: string
}) {
  const [language, setLanguage] = useState<Language>((locale as Language) || "en")

  const t = (key: string): string => {
    return translations[language][key as keyof (typeof translations)["en"]] || key
  }

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      <div className={language === "ar" ? "font-cairo" : "font-inter"} dir={language === "ar" ? "rtl" : "ltr"}>
        {children}
      </div>
    </LanguageContext.Provider>
  )
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider")
  }
  return context
}
