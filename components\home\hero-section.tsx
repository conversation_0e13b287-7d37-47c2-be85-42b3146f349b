"use client"

import { motion } from "framer-motion"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>R<PERSON>, Shield, Zap, Star } from "lucide-react"
import { useLanguage } from "@/components/providers/language-provider"

export function HeroSection() {
  const { language, t } = useLanguage()

  return (
    <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-black via-zinc-900 to-orange-900 overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]" />

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-6"
          >
            <div className="flex items-center justify-center space-x-2 mb-6">
              <div className="flex items-center space-x-1">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 fill-orange-400 text-orange-400" />
                ))}
              </div>
              <span className="text-zinc-300 text-sm">
                {language === "en" ? "Trusted by 10,000+ gamers" : "موثوق من قبل أكثر من 10,000 لاعب"}
              </span>
            </div>

            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white leading-tight">
              {language === "en" ? (
                <>
                  Premium{" "}
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600">
                    PUBG
                  </span>
                  <br />
                  Accounts & Hacks
                </>
              ) : (
                <>
                  حسابات وهاكات{" "}
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600">
                    PUBG
                  </span>
                  <br />
                  مميزة
                </>
              )}
            </h1>

            <p className="text-xl md:text-2xl text-zinc-300 max-w-2xl mx-auto">
              {language === "en"
                ? "Get high-level accounts with rare skins and professional gaming hacks. Instant delivery, secure transactions, 24/7 support."
                : "احصل على حسابات عالية المستوى مع أشكال نادرة وهاكات ألعاب احترافية. تسليم فوري، معاملات آمنة، دعم على مدار الساعة."}
            </p>

            <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mt-8">
              <Link href="/accounts">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-8 py-4 text-lg"
                >
                  {language === "en" ? "Browse Accounts" : "تصفح الحسابات"}
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
              </Link>
              <Link href="/hacks">
                <Button
                  variant="outline"
                  size="lg"
                  className="border-orange-400 text-orange-400 hover:bg-orange-400 hover:text-white px-8 py-4 text-lg bg-transparent"
                >
                  {language === "en" ? "View Hacks" : "عرض الهاكات"}
                </Button>
              </Link>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-16">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="flex items-center justify-center space-x-3 text-zinc-300"
              >
                <Shield className="w-8 h-8 text-orange-400" />
                <span className="text-lg">{language === "en" ? "Secure & Safe" : "آمن ومضمون"}</span>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="flex items-center justify-center space-x-3 text-zinc-300"
              >
                <Zap className="w-8 h-8 text-orange-400" />
                <span className="text-lg">{language === "en" ? "Instant Delivery" : "تسليم فوري"}</span>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="flex items-center justify-center space-x-3 text-zinc-300"
              >
                <Star className="w-8 h-8 text-orange-400" />
                <span className="text-lg">{language === "en" ? "24/7 Support" : "دعم على مدار الساعة"}</span>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
