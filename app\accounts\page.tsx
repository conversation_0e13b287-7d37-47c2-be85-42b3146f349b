import type { Metadata } from "next"
import { AccountsGrid } from "@/components/accounts/accounts-grid"
import { AccountsFilters } from "@/components/accounts/accounts-filters"
import { PageHeader } from "@/components/ui/page-header"
import { Account } from "@/types/account"

export const metadata: Metadata = {
  title: "PUBG Accounts - Premium Gaming Accounts | PUBG Store",
  description:
    "Browse our collection of premium PUBG accounts. High levels, rare skins, and competitive ranks available. Instant delivery guaranteed.",
  openGraph: {
    title: "PUBG Accounts - Premium Gaming Accounts",
    description:
      "Browse our collection of premium PUBG accounts. High levels, rare skins, and competitive ranks available.",
    url: "https://pubgstore.com/accounts",
  },
}

// ## TODO: Fetch accounts from Supabase: 'accounts' table with filters
const getAccounts = async (searchParams: any) => {
  // Placeholder data - replace with Supabase query using new Account structure
  return [
    {
      id: "1",
      name: {
        en: "Premium PUBG Mobile Account",
        ar: "حساب PUBG موبايل مميز"
      },
      description: {
        en: "High-level premium account with rare skins, exclusive items, and competitive achievements. Perfect for serious players.",
        ar: "حساب مميز عالي المستوى مع اسكنات نادرة وعناصر حصرية وإنجازات تنافسية. مثالي للاعبين الجادين."
      },
      price: 299.99,
      status: "active" as const,
      createdAt: "2024-01-15T10:30:00Z",
      updatedAt: "2024-01-15T10:30:00Z",
      imageUrl: "/placeholder.svg?height=300&width=400",
      isFeatured: true
    },
    {
      id: "2",
      name: {
        en: "PUBG PC Elite Account",
        ar: "حساب PUBG PC النخبة"
      },
      description: {
        en: "Professional gaming account with exclusive PC items, rare weapon skins, and competitive ranking history.",
        ar: "حساب ألعاب احترافي مع عناصر PC حصرية واسكنات أسلحة نادرة وتاريخ تصنيف تنافسي."
      },
      price: 499.99,
      status: "active" as const,
      createdAt: "2024-01-10T14:20:00Z",
      updatedAt: "2024-01-10T14:20:00Z",
      imageUrl: "/placeholder.svg?height=300&width=400",
      isFeatured: true
    },
    {
      id: "3",
      name: {
        en: "PUBG Starter Account",
        ar: "حساب PUBG للمبتدئين"
      },
      description: {
        en: "Great starter account with good collection of items and skins. Perfect for new players looking to get ahead.",
        ar: "حساب رائع للمبتدئين مع مجموعة جيدة من العناصر والاسكنات. مثالي للاعبين الجدد الذين يريدون التقدم."
      },
      price: 149.99,
      status: "sold" as const,
      createdAt: "2024-01-05T09:15:00Z",
      updatedAt: "2024-01-20T16:45:00Z",
      imageUrl: "/placeholder.svg?height=300&width=400",
      isFeatured: false
    },
  ]
}

export default async function AccountsPage({
  searchParams,
}: {
  searchParams: { [key: string]: string | string[] | undefined }
}) {
  const accounts = await getAccounts(searchParams)

  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    name: "PUBG Accounts",
    description: "Premium PUBG gaming accounts collection",
    url: "https://pubgstore.com/accounts",
    mainEntity: {
      "@type": "ItemList",
      itemListElement: accounts.map((account, index) => ({
        "@type": "Product",
        position: index + 1,
        name: account.name.en,
        description: account.description.en,
        offers: {
          "@type": "Offer",
          price: account.price,
          priceCurrency: "USD",
          availability: account.status === "active" ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
        },
      })),
    },
  }

  return (
    <>
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }} />
      <div className="min-h-screen bg-zinc-900">
        <div className="container mx-auto px-4 py-8">
          <PageHeader
            title="PUBG Accounts"
            description="Premium gaming accounts with high levels, rare skins, and competitive ranks"
          />
          <div className="flex flex-col lg:flex-row gap-8">
            <aside className="lg:w-1/4">
              <AccountsFilters />
            </aside>
            <main className="lg:w-3/4">
              <AccountsGrid accounts={accounts} />
            </main>
          </div>
        </div>
      </div>
    </>
  )
}
