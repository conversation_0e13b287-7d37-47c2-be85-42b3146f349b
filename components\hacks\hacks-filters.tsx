"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { X, Filter, Smartphone, Monitor, Shield } from "lucide-react"
import { useLanguage } from "@/components/providers/language-provider"
import { HackFilters, HackType, Platform, RootStatus, JailbreakStatus } from "@/types/hack"

interface HacksFiltersProps {
  onFiltersChange?: (filters: HackFilters) => void
}

export function HacksFilters({ onFiltersChange }: HacksFiltersProps) {
  const { language } = useLanguage()
  const [filters, setFilters] = useState<HackFilters>({})
  const [priceRange, setPriceRange] = useState([0, 100])
  const [showFilters, setShowFilters] = useState(false)

  const updateFilters = (newFilters: Partial<HackFilters>) => {
    const updatedFilters = { ...filters, ...newFilters }
    setFilters(updatedFilters)
    onFiltersChange?.(updatedFilters)
  }

  const clearFilters = () => {
    const clearedFilters: HackFilters = {}
    setFilters(clearedFilters)
    setPriceRange([0, 100])
    onFiltersChange?.(clearedFilters)
  }

  const activeFiltersCount = Object.keys(filters).filter(key =>
    filters[key as keyof HackFilters] !== undefined &&
    filters[key as keyof HackFilters] !== ""
  ).length

  const handlePriceChange = (value: number[]) => {
    setPriceRange(value)
    updateFilters({ priceMin: value[0], priceMax: value[1] })
  }

  return (
    <div className="space-y-4">
      {/* Mobile Filter Toggle */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => setShowFilters(!showFilters)}
          className="lg:hidden"
        >
          <Filter className="w-4 h-4 mr-2" />
          {language === "en" ? "Filters" : "المرشحات"}
          {activeFiltersCount > 0 && (
            <Badge variant="secondary" className="ml-2">
              {activeFiltersCount}
            </Badge>
          )}
        </Button>

        {activeFiltersCount > 0 && (
          <Button variant="ghost" size="sm" onClick={clearFilters}>
            <X className="w-4 h-4 mr-1" />
            {language === "en" ? "Clear" : "مسح"}
          </Button>
        )}
      </div>

      {/* Compact Filter Bar */}
      <div className={`${showFilters ? 'block' : 'hidden'} lg:block`}>
        <div className="bg-background border border-border rounded-lg p-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">

            {/* Type Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">
                {language === "en" ? "Type" : "النوع"}
              </label>
              <Select
                value={filters.type || ""}
                onValueChange={(value: HackType) =>
                  updateFilters({ type: value || undefined })
                }
              >
                <SelectTrigger className="h-9">
                  <SelectValue placeholder={language === "en" ? "All Types" : "جميع الأنواع"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">
                    {language === "en" ? "All Types" : "جميع الأنواع"}
                  </SelectItem>
                  <SelectItem value="mobile">
                    <div className="flex items-center">
                      <Smartphone className="w-4 h-4 mr-2" />
                      {language === "en" ? "Mobile" : "موبايل"}
                    </div>
                  </SelectItem>
                  <SelectItem value="pc">
                    <div className="flex items-center">
                      <Monitor className="w-4 h-4 mr-2" />
                      {language === "en" ? "PC" : "كمبيوتر"}
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Platform Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">
                {language === "en" ? "Platform" : "المنصة"}
              </label>
              <Select
                value={filters.platform || ""}
                onValueChange={(value: Platform) =>
                  updateFilters({ platform: value || undefined })
                }
              >
                <SelectTrigger className="h-9">
                  <SelectValue placeholder={language === "en" ? "All Platforms" : "جميع المنصات"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">
                    {language === "en" ? "All Platforms" : "جميع المنصات"}
                  </SelectItem>
                  <SelectItem value="android">Android</SelectItem>
                  <SelectItem value="ios">iOS</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Status Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">
                {language === "en" ? "Status" : "الحالة"}
              </label>
              <Select
                value={filters.status || ""}
                onValueChange={(value) =>
                  updateFilters({ status: value as any || undefined })
                }
              >
                <SelectTrigger className="h-9">
                  <SelectValue placeholder={language === "en" ? "All Status" : "جميع الحالات"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">
                    {language === "en" ? "All Status" : "جميع الحالات"}
                  </SelectItem>
                  <SelectItem value="active">
                    {language === "en" ? "Active" : "نشط"}
                  </SelectItem>
                  <SelectItem value="maintenance">
                    {language === "en" ? "Maintenance" : "صيانة"}
                  </SelectItem>
                  <SelectItem value="inactive">
                    {language === "en" ? "Inactive" : "غير نشط"}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Root Status Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">
                {language === "en" ? "Root Status" : "حالة الروت"}
              </label>
              <Select
                value={filters.rootStatus || ""}
                onValueChange={(value: RootStatus) =>
                  updateFilters({ rootStatus: value || undefined })
                }
              >
                <SelectTrigger className="h-9">
                  <SelectValue placeholder={language === "en" ? "Any" : "أي"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">
                    {language === "en" ? "Any" : "أي"}
                  </SelectItem>
                  <SelectItem value="root">
                    <div className="flex items-center">
                      <Shield className="w-4 h-4 mr-2" />
                      {language === "en" ? "Root Required" : "يتطلب روت"}
                    </div>
                  </SelectItem>
                  <SelectItem value="non-root">
                    {language === "en" ? "No Root" : "بدون روت"}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Price Range */}
            <div className="space-y-2 sm:col-span-2">
              <label className="text-sm font-medium">
                {language === "en" ? "Price Range" : "نطاق السعر"}
              </label>
              <div className="px-2">
                <Slider
                  value={priceRange}
                  onValueChange={handlePriceChange}
                  max={100}
                  step={5}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground mt-1">
                  <span>${priceRange[0]}</span>
                  <span>${priceRange[1]}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap gap-2">
          {filters.type && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {filters.type}
              <X
                className="w-3 h-3 cursor-pointer"
                onClick={() => updateFilters({ type: undefined })}
              />
            </Badge>
          )}
          {filters.platform && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {filters.platform}
              <X
                className="w-3 h-3 cursor-pointer"
                onClick={() => updateFilters({ platform: undefined })}
              />
            </Badge>
          )}
          {filters.status && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {filters.status}
              <X
                className="w-3 h-3 cursor-pointer"
                onClick={() => updateFilters({ status: undefined })}
              />
            </Badge>
          )}
          {filters.rootStatus && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {filters.rootStatus}
              <X
                className="w-3 h-3 cursor-pointer"
                onClick={() => updateFilters({ rootStatus: undefined })}
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  )
}
