"use client"

import Link from "next/link"
import { Facebook, Twitter, Instagram, Youtube } from "lucide-react"
import { useLanguage } from "@/components/providers/language-provider"

export function Footer() {
  const { language, t } = useLanguage()

  const footerLinks = {
    products: {
      title: { en: "Products", ar: "المنتجات" },
      links: [
        { href: "/accounts", label: { en: "PUBG Accounts", ar: "حسابات PUBG" } },
        { href: "/hacks", label: { en: "Gaming Hacks", ar: "هاكات الألعاب" } },
      ],
    },
    support: {
      title: { en: "Support", ar: "الدعم" },
      links: [
        { href: "/contact", label: { en: "Contact Us", ar: "اتصل بنا" } },
        { href: "/faq", label: { en: "FAQ", ar: "الأسئلة الشائعة" } },
        { href: "/help", label: { en: "Help Center", ar: "مركز المساعدة" } },
      ],
    },
    legal: {
      title: { en: "Legal", ar: "قانوني" },
      links: [
        { href: "/privacy", label: { en: "Privacy Policy", ar: "سياسة الخصوصية" } },
        { href: "/terms", label: { en: "Terms of Service", ar: "شروط الخدمة" } },
        { href: "/refund", label: { en: "Refund Policy", ar: "سياسة الاسترداد" } },
      ],
    },
  }

  return (
    <footer className="bg-black border-t border-zinc-800">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">PG</span>
              </div>
              <span className="text-xl font-bold text-white">PUBG Store</span>
            </div>
            <p className="text-zinc-400 text-sm">
              {language === "en"
                ? "Your trusted source for premium PUBG accounts and professional gaming hacks."
                : "مصدرك الموثوق لحسابات PUBG المميزة وهاكات الألعاب الاحترافية."}
            </p>
            <div className="flex space-x-4">
              <Link href="#" className="text-zinc-400 hover:text-orange-400 transition-colors">
                <Facebook className="w-5 h-5" />
              </Link>
              <Link href="#" className="text-zinc-400 hover:text-orange-400 transition-colors">
                <Twitter className="w-5 h-5" />
              </Link>
              <Link href="#" className="text-zinc-400 hover:text-orange-400 transition-colors">
                <Instagram className="w-5 h-5" />
              </Link>
              <Link href="#" className="text-zinc-400 hover:text-orange-400 transition-colors">
                <Youtube className="w-5 h-5" />
              </Link>
            </div>
          </div>

          {/* Footer Links */}
          {Object.entries(footerLinks).map(([key, section]) => (
            <div key={key} className="space-y-4">
              <h3 className="text-white font-semibold">{section.title[language]}</h3>
              <ul className="space-y-2">
                {section.links.map((link) => (
                  <li key={link.href}>
                    <Link href={link.href} className="text-zinc-400 hover:text-orange-400 transition-colors text-sm">
                      {link.label[language]}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="border-t border-zinc-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-zinc-400 text-sm">
            © 2024 PUBG Store. {language === "en" ? "All rights reserved." : "جميع الحقوق محفوظة."}
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <Link href="/privacy" className="text-zinc-400 hover:text-orange-400 transition-colors text-sm">
              {language === "en" ? "Privacy" : "الخصوصية"}
            </Link>
            <Link href="/terms" className="text-zinc-400 hover:text-orange-400 transition-colors text-sm">
              {language === "en" ? "Terms" : "الشروط"}
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
}
