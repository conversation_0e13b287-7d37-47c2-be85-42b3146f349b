"use client"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { AccountsManagement } from "./accounts-management"
import { HacksManagement } from "./hacks-management"
import { UserManagement } from "./user-management"

export function AdminDashboard() {
  return (
    <div className="space-y-8">
      <Tabs defaultValue="accounts" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="accounts">Accounts Management</TabsTrigger>
          <TabsTrigger value="hacks">Hack Management</TabsTrigger>
          <TabsTrigger value="users">User Management</TabsTrigger>
        </TabsList>

        <TabsContent value="accounts" className="mt-6">
          <AccountsManagement />
        </TabsContent>

        <TabsContent value="hacks" className="mt-6">
          <HacksManagement />
        </TabsContent>

        <TabsContent value="users" className="mt-6">
          <UserManagement />
        </TabsContent>
      </Tabs>
    </div>
  )
}
