"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Plus, Search, Eye, Edit, Trash2, Star, StarOff, Download } from "lucide-react"
import { Hack, HackFilters, HackFormData, getHackSpecDisplayText } from "@/types/hack"
import { HackForm } from "./hack-form"

/**
 * Hack Management Component
 *
 * ## Database Integration Notes:
 * - This component will connect to hacks table with bilingual support
 * - Implement conditional field validation based on hack type
 * - Add real-time updates for hack status changes
 * - Include file upload functionality for hack files
 * - Track download statistics and popularity metrics
 */

export function HacksManagement() {
  // ## Database: Replace with actual API call to fetch hacks
  const [hacks, setHacks] = useState<Hack[]>([
    {
      id: "1",
      name: { en: "PUBG Mobile Aimbot Pro", ar: "ايمبوت PUBG موبايل برو" },
      type: "mobile",
      platform: "android",
      rootStatus: "root",
      price: 49.99,
      company: "GameHacks Inc",
      version: "2.1.5",
      description: {
        en: "Advanced aimbot with customizable settings for PUBG Mobile",
        ar: "ايمبوت متقدم مع إعدادات قابلة للتخصيص لـ PUBG موبايل"
      },
      status: "active",
      createdAt: "2024-01-15T10:30:00Z",
      updatedAt: "2024-01-20T14:45:00Z",
      imageUrl: "/placeholder.svg",
      isFeatured: true,
      downloadCount: 1250,
      lastUpdated: "2024-01-20"
    },
    {
      id: "2",
      name: { en: "iOS PUBG ESP Hack", ar: "هاك ESP لـ PUBG على iOS" },
      type: "mobile",
      platform: "ios",
      jailbreakStatus: "jailbroken",
      price: 39.99,
      company: "MobileHacks Ltd",
      version: "1.8.2",
      description: {
        en: "ESP wallhack for iOS devices with jailbreak",
        ar: "هاك ESP للأجهزة iOS مع الجيلبريك"
      },
      status: "active",
      createdAt: "2024-01-10T09:15:00Z",
      updatedAt: "2024-01-18T11:30:00Z",
      imageUrl: "/placeholder.svg",
      isFeatured: false,
      downloadCount: 890,
      lastUpdated: "2024-01-18"
    },
    {
      id: "3",
      name: { en: "PUBG PC Ultimate Hack", ar: "هاك PUBG PC النهائي" },
      type: "pc",
      price: 79.99,
      company: "PCGaming Tools",
      version: "3.0.1",
      description: {
        en: "Complete hack suite for PUBG PC with aimbot, ESP, and more",
        ar: "مجموعة هاكات كاملة لـ PUBG PC مع ايمبوت و ESP والمزيد"
      },
      status: "active",
      createdAt: "2024-01-05T16:20:00Z",
      updatedAt: "2024-01-22T10:15:00Z",
      imageUrl: "/placeholder.svg",
      isFeatured: true,
      downloadCount: 2100,
      lastUpdated: "2024-01-22"
    }
  ])

  const [searchTerm, setSearchTerm] = useState("")
  const [filters, setFilters] = useState<HackFilters>({})
  const [currentLanguage, setCurrentLanguage] = useState<'en' | 'ar'>('en')
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [editingHack, setEditingHack] = useState<Hack | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  const handleAddHack = () => {
    setEditingHack(null)
    setIsFormOpen(true)
  }

  const handleSaveHack = async (formData: HackFormData) => {
    setIsLoading(true)
    try {
      // ## Database: Save hack to Supabase
      console.log("Saving hack:", formData)

      if (editingHack) {
        // Update existing hack
        setHacks(hacks.map(hack =>
          hack.id === editingHack.id
            ? { ...hack, ...formData, updatedAt: new Date().toISOString() }
            : hack
        ))
      } else {
        // Create new hack
        const newHack: Hack = {
          ...formData,
          id: Date.now().toString(), // ## Database: Use proper UUID from Supabase
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          downloadCount: 0,
          lastUpdated: new Date().toISOString().split('T')[0],
          status: "active"
        }
        setHacks([...hacks, newHack])
      }

      setIsFormOpen(false)
      setEditingHack(null)
    } catch (error) {
      console.error("Error saving hack:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancelForm = () => {
    setIsFormOpen(false)
    setEditingHack(null)
  }

  const handleViewHack = (id: string) => {
    // ## Database: Fetch and display hack details
    console.log("View hack:", id)
  }

  const handleEditHack = (id: string) => {
    const hack = hacks.find(h => h.id === id)
    if (hack) {
      setEditingHack(hack)
      setIsFormOpen(true)
    }
  }

  const handleDeleteHack = (id: string) => {
    // ## Database: Soft delete hack (set status to inactive)
    setHacks(hacks.filter((hack) => hack.id !== id))
  }

  const handleToggleFeatured = (id: string) => {
    // ## Database: Update is_featured field
    setHacks(hacks.map(hack =>
      hack.id === id
        ? { ...hack, isFeatured: !hack.isFeatured }
        : hack
    ))
  }

  // Filter hacks based on search and filters
  const filteredHacks = hacks.filter((hack) => {
    const matchesSearch = searchTerm === "" ||
      hack.name.en.toLowerCase().includes(searchTerm.toLowerCase()) ||
      hack.name.ar.includes(searchTerm) ||
      hack.description.en.toLowerCase().includes(searchTerm.toLowerCase()) ||
      hack.description.ar.includes(searchTerm) ||
      hack.company.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesType = !filters.type || hack.type === filters.type
    const matchesPlatform = !filters.platform || hack.platform === filters.platform
    const matchesStatus = !filters.status || hack.status === filters.status
    const matchesCompany = !filters.company || hack.company.toLowerCase().includes(filters.company.toLowerCase())
    const matchesFeatured = filters.isFeatured === undefined || hack.isFeatured === filters.isFeatured
    const matchesPrice = (!filters.priceMin || hack.price >= filters.priceMin) &&
                        (!filters.priceMax || hack.price <= filters.priceMax)

    return matchesSearch && matchesType && matchesPlatform && matchesStatus &&
           matchesCompany && matchesFeatured && matchesPrice
  })

  return (
    <>
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Hack Management</CardTitle>
          <div className="flex items-center space-x-2">
            <Select value={currentLanguage} onValueChange={(value: 'en' | 'ar') => setCurrentLanguage(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en">English</SelectItem>
                <SelectItem value="ar">العربية</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={handleAddHack} className="bg-orange-500 hover:bg-orange-600">
              <Plus className="w-4 h-4 mr-2" />
              Add Hack
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Search and Filters */}
        <div className="flex items-center space-x-4 mb-6">
          <div className="flex items-center space-x-2 flex-1">
            <Search className="w-4 h-4 text-zinc-400" />
            <Input
              placeholder="Search hacks by name, description, or company..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
          </div>

          <Select value={filters.type || "all"} onValueChange={(value) =>
            setFilters({...filters, type: value === "all" ? undefined : value as Hack['type']})
          }>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="mobile">Mobile</SelectItem>
              <SelectItem value="pc">PC</SelectItem>
            </SelectContent>
          </Select>

          <Select value={filters.platform || "all"} onValueChange={(value) =>
            setFilters({...filters, platform: value === "all" ? undefined : value as Hack['platform']})
          }>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Platform" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Platforms</SelectItem>
              <SelectItem value="android">Android</SelectItem>
              <SelectItem value="ios">iOS</SelectItem>
            </SelectContent>
          </Select>

          <Select value={filters.status || "all"} onValueChange={(value) =>
            setFilters({...filters, status: value === "all" ? undefined : value as Hack['status']})
          }>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
              <SelectItem value="maintenance">Maintenance</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Hacks Table */}
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Specifications</TableHead>
              <TableHead>Company</TableHead>
              <TableHead>Price</TableHead>
              <TableHead>Downloads</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Featured</TableHead>
              <TableHead>Last Updated</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredHacks.map((hack) => (
              <TableRow key={hack.id}>
                <TableCell className="font-medium">
                  <div className="max-w-48">
                    <div className="font-semibold text-white">
                      {hack.name[currentLanguage]}
                    </div>
                    {currentLanguage === 'en' && hack.name.ar && (
                      <div className="text-sm text-zinc-400 mt-1">
                        {hack.name.ar}
                      </div>
                    )}
                    {currentLanguage === 'ar' && hack.name.en && (
                      <div className="text-sm text-zinc-400 mt-1">
                        {hack.name.en}
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm">
                    <div className="text-zinc-300">
                      {getHackSpecDisplayText(hack, currentLanguage)}
                    </div>
                    <div className="text-zinc-400 mt-1">
                      {hack.description[currentLanguage].substring(0, 50)}...
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <span className="text-zinc-300 font-medium">
                    {hack.company}
                  </span>
                </TableCell>
                <TableCell>
                  <span className="font-semibold text-orange-400">
                    ${hack.price}
                  </span>
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-1">
                    <Download className="w-4 h-4 text-zinc-400" />
                    <span className="text-zinc-300 font-medium">
                      {hack.downloadCount.toLocaleString()}
                    </span>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant={
                    hack.status === "active" ? "default" :
                    hack.status === "maintenance" ? "secondary" : "destructive"
                  }>
                    {hack.status}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleToggleFeatured(hack.id)}
                    className={hack.isFeatured ? "text-orange-400" : "text-zinc-500"}
                  >
                    {hack.isFeatured ? <Star className="w-4 h-4 fill-current" /> : <StarOff className="w-4 h-4" />}
                  </Button>
                </TableCell>
                <TableCell>
                  <span className="text-sm text-zinc-400">
                    {new Date(hack.lastUpdated).toLocaleDateString()}
                  </span>
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm" onClick={() => handleViewHack(hack.id)}>
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => handleEditHack(hack.id)}>
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteHack(hack.id)}
                      className="text-red-500 hover:text-red-500/80"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        {filteredHacks.length === 0 && (
          <div className="text-center py-8">
            <p className="text-zinc-400">No hacks found matching your criteria.</p>
          </div>
        )}
      </CardContent>
    </Card>

    {/* Hack Form Modal */}
    <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {editingHack ? "Edit Hack" : "Create New Hack"}
          </DialogTitle>
        </DialogHeader>
        <HackForm
          hack={editingHack ? {
            name: editingHack.name,
            description: editingHack.description,
            type: editingHack.type,
            platform: editingHack.platform,
            rootStatus: editingHack.rootStatus,
            jailbreakStatus: editingHack.jailbreakStatus,
            price: editingHack.price,
            company: editingHack.company,
            version: editingHack.version,
            imageUrl: editingHack.imageUrl,
            isFeatured: editingHack.isFeatured
          } : undefined}
          onSave={handleSaveHack}
          onCancel={handleCancelForm}
          isLoading={isLoading}
        />
      </DialogContent>
    </Dialog>
  </>
  )
}
