"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import Image from "next/image"
import { <PERSON>, <PERSON>Conte<PERSON>, CardFooter, CardHeader } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Star, ShoppingCart, Download, Eye, Smartphone, Monitor } from "lucide-react"
import { useLanguage } from "@/components/providers/language-provider"
import { ProductViewModal } from "@/components/ui/product-view-modal"

import { Hack, getHackSpecDisplayText } from "@/types/hack"

interface HacksGridProps {
  hacks: Hack[]
}

export function HacksGrid({ hacks }: HacksGridProps) {
  const { language } = useLanguage()
  const [selectedHack, setSelectedHack] = useState<Hack | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  const handleViewHack = (hack: Hack) => {
    setSelectedHack(hack)
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setSelectedHack(null)
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  }

  return (
    <>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6"
      >
        {hacks.map((hack) => (
          <motion.div key={hack.id} variants={itemVariants}>
            <Card className="bg-background border-border hover:border-orange-400 transition-all duration-300 group h-full flex flex-col">
              <CardHeader className="p-0">
                <div className="relative overflow-hidden rounded-t-lg">
                  <Image
                    src={hack.imageUrl || "/placeholder.svg"}
                    alt={hack.name[language]}
                    width={300}
                    height={200}
                    className="w-full h-32 sm:h-40 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  {hack.isFeatured && (
                    <Badge className="absolute top-2 left-2 bg-orange-500 text-white text-xs">
                      <Star className="w-3 h-3 mr-1" />
                      {language === "en" ? "Featured" : "مميز"}
                    </Badge>
                  )}
                  <Badge
                    className="absolute top-2 right-2 text-xs"
                    variant={hack.status === "active" ? "default" : "secondary"}
                  >
                    {hack.status}
                  </Badge>
                </div>
              </CardHeader>

              <CardContent className="p-3 sm:p-4 flex-1">
                <div className="space-y-3">
                  <div>
                    <h3 className="font-bold text-sm sm:text-base line-clamp-2 mb-1">
                      {hack.name[language]}
                    </h3>
                    <p className="text-muted-foreground text-xs sm:text-sm line-clamp-2">
                      {hack.description[language]}
                    </p>
                  </div>

                  <div className="flex items-center justify-between text-xs">
                    <div className="flex items-center space-x-1">
                      {hack.type === 'pc' ? (
                        <Monitor className="w-3 h-3" />
                      ) : (
                        <Smartphone className="w-3 h-3" />
                      )}
                      <span className="capitalize">{hack.type}</span>
                      {hack.platform && (
                        <>
                          <span>•</span>
                          <span className="capitalize">{hack.platform}</span>
                        </>
                      )}
                    </div>
                    <div className="text-muted-foreground">
                      v{hack.version}
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                      <Download className="w-3 h-3" />
                      <span>{hack.downloadCount > 1000 ? `${(hack.downloadCount / 1000).toFixed(1)}k` : hack.downloadCount}</span>
                    </div>
                    <div className="text-lg sm:text-xl font-bold text-orange-400">
                      ${hack.price}
                    </div>
                  </div>
                </div>
              </CardContent>

              <CardFooter className="p-3 sm:p-4 pt-0">
                <div className="w-full space-y-2">
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      className="flex-1 bg-orange-500 hover:bg-orange-600 text-white text-xs"
                      disabled={hack.status !== "active"}
                    >
                      <ShoppingCart className="w-3 h-3 mr-1" />
                      {language === "en" ? "Add" : "أضف"}
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="px-2"
                      onClick={() => handleViewHack(hack)}
                    >
                      <Eye className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </CardFooter>
            </Card>
          </motion.div>
        ))}
      </motion.div>

      <ProductViewModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        product={selectedHack}
        productType="hack"
      />
    </>
  )
}
