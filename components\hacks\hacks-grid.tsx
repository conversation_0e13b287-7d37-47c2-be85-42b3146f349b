"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Star, ShoppingCart, Download } from "lucide-react"
import { useLanguage } from "@/components/providers/language-provider"

import { Hack, getHackSpecDisplayText } from "@/types/hack"

interface HacksGridProps {
  hacks: Hack[]
}

export function HacksGrid({ hacks }: HacksGridProps) {
  const { language, t } = useLanguage()

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
    >
      {hacks.map((hack) => (
        <motion.div key={hack.id} variants={itemVariants}>
          <Card className="bg-black border-zinc-700 hover:border-orange-400 transition-all duration-300 group h-full flex flex-col">
            <CardHeader className="p-0">
              <div className="relative overflow-hidden rounded-t-lg">
                <Image
                  src={hack.imageUrl || "/placeholder.svg"}
                  alt={hack.name[language]}
                  width={400}
                  height={300}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                {hack.isFeatured && (
                  <Badge className="absolute top-4 left-4 bg-orange-500 text-white">
                    {language === "en" ? "Featured" : "مميز"}
                  </Badge>
                )}
                <Badge className="absolute top-4 right-4 bg-green-500 text-white">
                  {language === "en" ? "Safe" : "آمن"}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="p-6 flex-1">
              <h3 className="text-xl font-semibold text-white mb-2">
                {hack.name[language]}
              </h3>

              <div className="flex items-center space-x-4 mb-4 text-sm text-zinc-400">
                <span>{getHackSpecDisplayText(hack, language)}</span>
              </div>

              <div className="mb-4">
                <p className="text-zinc-400 text-sm leading-relaxed">
                  {hack.description[language]}
                </p>
              </div>

              <div className="flex items-center space-x-4 mb-4 text-sm">
                <div className="flex items-center space-x-1">
                  <Download className="w-4 h-4 text-orange-400" />
                  <span className="text-zinc-300">
                    {hack.downloadCount.toLocaleString()} {language === "en" ? "downloads" : "تحميل"}
                  </span>
                </div>
                <span className="text-zinc-500">•</span>
                <span className="text-zinc-400">
                  {language === "en" ? "by" : "من"} {hack.company}
                </span>
              </div>

              <div className="text-xs text-zinc-500 mb-4">
                {language === "en" ? "Last updated:" : "آخر تحديث:"} {new Date(hack.lastUpdated).toLocaleDateString()}
              </div>
            </CardContent>
            <CardFooter className="p-6 pt-0">
              <div className="w-full space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-2xl font-bold text-orange-400">${hack.price}</span>
                  </div>
                  <div className="text-right">
                    <Badge variant={
                      hack.status === "active" ? "default" :
                      hack.status === "maintenance" ? "secondary" : "destructive"
                    }>
                      {hack.status}
                    </Badge>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button
                    className="flex-1 bg-orange-500 hover:bg-orange-600 text-white"
                    disabled={hack.status !== "active"}
                  >
                    <ShoppingCart className="w-4 h-4 mr-2" />
                    {language === "en" ? "Add to Cart" : "أضف للسلة"}
                  </Button>
                  <Button
                    variant="outline"
                    className="flex-1 border-orange-400 text-orange-400 hover:bg-orange-400 hover:text-white bg-transparent"
                    disabled={hack.status !== "active"}
                  >
                    <Download className="w-4 h-4 mr-2" />
                    {language === "en" ? "Buy Now" : "اشتري الآن"}
                  </Button>
                </div>
              </div>
            </CardFooter>
          </Card>
        </motion.div>
      ))}
    </motion.div>
  )
}
