"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import Image from "next/image"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter, CardHeader } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Star, CreditCard, Eye, Calendar } from "lucide-react"
import { useLanguage } from "@/components/providers/language-provider"
import { ProductViewModal } from "@/components/ui/product-view-modal"
import { PurchaseFlow } from "@/components/purchase/purchase-flow"

import { Account } from "@/types/account"

interface AccountsGridProps {
  accounts: Account[]
}

export function AccountsGrid({ accounts }: AccountsGridProps) {
  const { language } = useLanguage()
  const [selectedAccount, setSelectedAccount] = useState<Account | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isPurchaseOpen, setIsPurchaseOpen] = useState(false)
  const [purchaseAccount, setPurchaseAccount] = useState<Account | null>(null)

  const handleViewAccount = (account: Account) => {
    setSelectedAccount(account)
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setSelectedAccount(null)
  }

  const handleBuyNow = (account: Account) => {
    setPurchaseAccount(account)
    setIsPurchaseOpen(true)
  }

  const handleClosePurchase = () => {
    setIsPurchaseOpen(false)
    setPurchaseAccount(null)
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  }

  return (
    <>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6"
      >
        {accounts.map((account) => (
          <motion.div key={account.id} variants={itemVariants}>
            <Card
              className="bg-background border-border hover:border-orange-400 transition-all duration-300 group h-full flex flex-col cursor-pointer"
              onClick={() => handleViewAccount(account)}
            >
              <CardHeader className="p-0">
                <div className="relative overflow-hidden rounded-t-lg">
                  <Image
                    src={account.imageUrl || "/placeholder.svg"}
                    alt={account.name[language]}
                    width={300}
                    height={200}
                    className="w-full h-32 sm:h-40 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  {account.isFeatured && (
                    <Badge className="absolute top-2 left-2 bg-orange-500 text-white text-xs">
                      <Star className="w-3 h-3 mr-1" />
                      {language === "en" ? "Featured" : "مميز"}
                    </Badge>
                  )}
                  <Badge
                    className="absolute top-2 right-2 text-xs"
                    variant={
                      account.status === "active" ? "default" :
                      account.status === "sold" ? "destructive" : "secondary"
                    }
                  >
                    {account.status}
                  </Badge>
                </div>
              </CardHeader>

              <CardContent className="p-3 sm:p-4 flex-1">
                <div className="space-y-3">
                  <div>
                    <h3 className="font-bold text-sm sm:text-base line-clamp-2 mb-1">
                      {account.name[language]}
                    </h3>
                    <p className="text-muted-foreground text-xs sm:text-sm line-clamp-3">
                      {account.description[language]}
                    </p>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                      <Calendar className="w-3 h-3" />
                      <span>{new Date(account.createdAt).toLocaleDateString()}</span>
                    </div>
                    <div className="text-lg sm:text-xl font-bold text-orange-400">
                      ${account.price}
                    </div>
                  </div>
                </div>
              </CardContent>

              <CardFooter className="p-3 sm:p-4 pt-0">
                <div className="w-full">
                  <Button
                    size="sm"
                    className="w-full bg-orange-500 hover:bg-orange-600 text-white text-xs"
                    disabled={account.status !== "active"}
                    onClick={(e) => {
                      e.stopPropagation()
                      handleBuyNow(account)
                    }}
                  >
                    <CreditCard className="w-3 h-3 mr-1" />
                    {language === "en" ? "Buy Now" : "اشتري الآن"}
                  </Button>
                </div>
              </CardFooter>
            </Card>
          </motion.div>
        ))}
      </motion.div>

      <ProductViewModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        product={selectedAccount}
        productType="account"
      />

      <PurchaseFlow
        isOpen={isPurchaseOpen}
        onClose={handleClosePurchase}
        product={purchaseAccount}
        productType="account"
      />
    </>
  )
}
