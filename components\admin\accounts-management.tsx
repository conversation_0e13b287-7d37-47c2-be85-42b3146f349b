"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Plus, Search, Eye, Edit, Trash2, Star, StarOff } from "lucide-react"
import { Account, AccountFilters, AccountFormData } from "@/types/account"
import { AccountForm } from "./account-form"

/**
 * Account Management Component
 *
 * ## Database Integration Notes:
 * - This component will connect to accounts table with bilingual support
 * - Implement pagination for large datasets
 * - Add real-time updates for account status changes
 * - Include image upload functionality for account images
 */

export function AccountsManagement() {
  // ## Database: Replace with actual API call to fetch accounts
  const [accounts, setAccounts] = useState<Account[]>([
    {
      id: "1",
      name: { en: "Premium PUBG Mobile Account", ar: "حساب PUBG موبايل مميز" },
      description: {
        en: "High-level account with rare skins and achievements",
        ar: "حساب عالي المستوى مع اسكنات نادرة وإنجازات"
      },
      price: 299.99,
      status: "active",
      createdAt: "2024-01-15T10:30:00Z",
      updatedAt: "2024-01-15T10:30:00Z",
      imageUrl: "/placeholder.svg",
      isFeatured: true,
      stockQuantity: 5
    },
    {
      id: "2",
      name: { en: "PUBG PC Elite Account", ar: "حساب PUBG PC النخبة" },
      description: {
        en: "Professional gaming account with exclusive items",
        ar: "حساب ألعاب احترافي مع عناصر حصرية"
      },
      price: 199.99,
      status: "active",
      createdAt: "2024-01-10T14:20:00Z",
      updatedAt: "2024-01-10T14:20:00Z",
      imageUrl: "/placeholder.svg",
      isFeatured: false,
      stockQuantity: 3
    }
  ])

  const [searchTerm, setSearchTerm] = useState("")
  const [filters, setFilters] = useState<AccountFilters>({})
  const [currentLanguage, setCurrentLanguage] = useState<'en' | 'ar'>('en')
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [editingAccount, setEditingAccount] = useState<Account | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  const handleAddAccount = () => {
    setEditingAccount(null)
    setIsFormOpen(true)
  }

  const handleSaveAccount = async (formData: AccountFormData) => {
    setIsLoading(true)
    try {
      // ## Database: Save account to Supabase
      console.log("Saving account:", formData)

      if (editingAccount) {
        // Update existing account
        setAccounts(accounts.map(acc =>
          acc.id === editingAccount.id
            ? { ...acc, ...formData, updatedAt: new Date().toISOString() }
            : acc
        ))
      } else {
        // Create new account
        const newAccount: Account = {
          ...formData,
          id: Date.now().toString(), // ## Database: Use proper UUID from Supabase
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          status: "active" as const
        }
        setAccounts([...accounts, newAccount])
      }

      setIsFormOpen(false)
      setEditingAccount(null)
    } catch (error) {
      console.error("Error saving account:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancelForm = () => {
    setIsFormOpen(false)
    setEditingAccount(null)
  }

  const handleViewAccount = (id: string) => {
    // ## Database: Fetch and display account details
    console.log("View account:", id)
  }

  const handleEditAccount = (id: string) => {
    const account = accounts.find(acc => acc.id === id)
    if (account) {
      setEditingAccount(account)
      setIsFormOpen(true)
    }
  }

  const handleDeleteAccount = (id: string) => {
    // ## Database: Soft delete account (set status to inactive)
    setAccounts(accounts.filter((account) => account.id !== id))
  }

  const handleToggleFeatured = (id: string) => {
    // ## Database: Update is_featured field
    setAccounts(accounts.map(account =>
      account.id === id
        ? { ...account, isFeatured: !account.isFeatured }
        : account
    ))
  }

  // Filter accounts based on search and filters
  const filteredAccounts = accounts.filter((account) => {
    const matchesSearch = searchTerm === "" ||
      account.name.en.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.name.ar.includes(searchTerm) ||
      account.description.en.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.description.ar.includes(searchTerm)

    const matchesStatus = !filters.status || account.status === filters.status
    const matchesFeatured = filters.isFeatured === undefined || account.isFeatured === filters.isFeatured
    const matchesPrice = (!filters.priceMin || account.price >= filters.priceMin) &&
                        (!filters.priceMax || account.price <= filters.priceMax)

    return matchesSearch && matchesStatus && matchesFeatured && matchesPrice
  })

  return (
    <>
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Account Management</CardTitle>
          <div className="flex items-center space-x-2">
            <Select value={currentLanguage} onValueChange={(value: 'en' | 'ar') => setCurrentLanguage(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en">English</SelectItem>
                <SelectItem value="ar">العربية</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={handleAddAccount} className="bg-orange-500 hover:bg-orange-600">
              <Plus className="w-4 h-4 mr-2" />
              Add Account
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Search and Filters */}
        <div className="flex items-center space-x-4 mb-6">
          <div className="flex items-center space-x-2 flex-1">
            <Search className="w-4 h-4 text-zinc-400" />
            <Input
              placeholder="Search accounts by name or description..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
          </div>

          <Select value={filters.status || "all"} onValueChange={(value) =>
            setFilters({...filters, status: value === "all" ? undefined : value as Account['status']})
          }>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
              <SelectItem value="sold">Sold</SelectItem>
            </SelectContent>
          </Select>

          <Select value={filters.isFeatured?.toString() || "all"} onValueChange={(value) =>
            setFilters({...filters, isFeatured: value === "all" ? undefined : value === "true"})
          }>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Featured" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="true">Featured</SelectItem>
              <SelectItem value="false">Not Featured</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Accounts Table */}
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Price</TableHead>
              <TableHead>Stock</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Featured</TableHead>
              <TableHead>Created</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredAccounts.map((account) => (
              <TableRow key={account.id}>
                <TableCell className="font-medium">
                  <div className="max-w-48">
                    <div className="font-semibold text-white">
                      {account.name[currentLanguage]}
                    </div>
                    {currentLanguage === 'en' && account.name.ar && (
                      <div className="text-sm text-zinc-400 mt-1">
                        {account.name.ar}
                      </div>
                    )}
                    {currentLanguage === 'ar' && account.name.en && (
                      <div className="text-sm text-zinc-400 mt-1">
                        {account.name.en}
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="max-w-64">
                    <div className="text-sm text-zinc-300">
                      {account.description[currentLanguage]}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <span className="font-semibold text-orange-400">
                    ${account.price}
                  </span>
                </TableCell>
                <TableCell>
                  <span className={`font-medium ${account.stockQuantity > 0 ? 'text-green-500' : 'text-red-500'}`}>
                    {account.stockQuantity}
                  </span>
                </TableCell>
                <TableCell>
                  <Badge variant={
                    account.status === "active" ? "default" :
                    account.status === "sold" ? "destructive" : "secondary"
                  }>
                    {account.status}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleToggleFeatured(account.id)}
                    className={account.isFeatured ? "text-orange-400" : "text-zinc-500"}
                  >
                    {account.isFeatured ? <Star className="w-4 h-4 fill-current" /> : <StarOff className="w-4 h-4" />}
                  </Button>
                </TableCell>
                <TableCell>
                  <span className="text-sm text-zinc-400">
                    {new Date(account.createdAt).toLocaleDateString()}
                  </span>
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm" onClick={() => handleViewAccount(account.id)}>
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => handleEditAccount(account.id)}>
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteAccount(account.id)}
                      className="text-red-500 hover:text-red-500/80"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        {filteredAccounts.length === 0 && (
          <div className="text-center py-8">
            <p className="text-zinc-400">No accounts found matching your criteria.</p>
          </div>
        )}
      </CardContent>
    </Card>

    {/* Account Form Modal */}
    <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {editingAccount ? "Edit Account" : "Create New Account"}
          </DialogTitle>
        </DialogHeader>
        <AccountForm
          account={editingAccount ? {
            name: editingAccount.name,
            description: editingAccount.description,
            price: editingAccount.price,
            imageUrl: editingAccount.imageUrl,
            isFeatured: editingAccount.isFeatured
          } : undefined}
          onSave={handleSaveAccount}
          onCancel={handleCancelForm}
          isLoading={isLoading}
        />
      </DialogContent>
    </Dialog>
  </>
  )
}
