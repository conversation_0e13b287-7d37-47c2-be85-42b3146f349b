import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Cairo, Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/providers/theme-provider"
import { LanguageProvider } from "@/components/providers/language-provider"
import { Navigation } from "@/components/layout/navigation"
import { Footer } from "@/components/layout/footer"

const inter = Inter({ subsets: ["latin"], variable: "--font-inter" })
const cairo = Cairo({ subsets: ["arabic", "latin"], variable: "--font-cairo" })

export const metadata: Metadata = {
  title: "PUBG Store - Premium Accounts & Tools",
  description:
    "Get premium PUBG accounts and professional gaming tools. Fast delivery, secure transactions, 24/7 support.",
  keywords: "PUBG, accounts, gaming, tools, premium, store",
  authors: [{ name: "PUBG Store" }],
  creator: "PUBG Store",
  publisher: "PUBG Store",
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://pubgstore.com",
    title: "PUBG Store - Premium Accounts & Tools",
    description:
      "Get premium PUBG accounts and professional gaming tools. Fast delivery, secure transactions, 24/7 support.",
    siteName: "PUBG Store",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "PUBG Store",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "PUBG Store - Premium Accounts & Tools",
    description:
      "Get premium PUBG accounts and professional gaming tools. Fast delivery, secure transactions, 24/7 support.",
    images: ["/og-image.jpg"],
  },
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
  params: { locale },
}: {
  children: React.ReactNode
  params: { locale: string }
}) {
  return (
    <html lang={locale || "en"} suppressHydrationWarning>
      <head>
        <link rel="canonical" href="https://pubgstore.com" />
        <link rel="sitemap" href="/sitemap.xml" />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebSite",
              name: "PUBG Store",
              url: "https://pubgstore.com",
              description: "Premium PUBG accounts and gaming tools store",
              potentialAction: {
                "@type": "SearchAction",
                target: "https://pubgstore.com/search?q={search_term_string}",
                "query-input": "required name=search_term_string",
              },
            }),
          }}
        />
      </head>
      <body className={`${inter.variable} ${cairo.variable} font-sans antialiased bg-zinc-900 text-white`}>
        <ThemeProvider>
          <LanguageProvider locale={locale}>
            <div className="min-h-screen flex flex-col">
              <Navigation />
              <main className="flex-1">{children}</main>
              <Footer />
            </div>
          </LanguageProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
