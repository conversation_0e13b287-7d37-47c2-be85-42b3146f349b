"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { X, Filter, Star, DollarSign } from "lucide-react"
import { useLanguage } from "@/components/providers/language-provider"
import { AccountFilters } from "@/types/account"

interface AccountsFiltersProps {
  onFiltersChange?: (filters: AccountFilters) => void
}

export function AccountsFilters({ onFiltersChange }: AccountsFiltersProps) {
  const { language } = useLanguage()
  const [filters, setFilters] = useState<AccountFilters>({})
  const [priceRange, setPriceRange] = useState([0, 500])
  const [showFilters, setShowFilters] = useState(false)

  const updateFilters = (newFilters: Partial<AccountFilters>) => {
    const updatedFilters = { ...filters, ...newFilters }
    setFilters(updatedFilters)
    onFiltersChange?.(updatedFilters)
  }

  const clearFilters = () => {
    const clearedFilters: AccountFilters = {}
    setFilters(clearedFilters)
    setPriceRange([0, 500])
    onFiltersChange?.(clearedFilters)
  }

  const activeFiltersCount = Object.keys(filters).filter(key =>
    filters[key as keyof AccountFilters] !== undefined &&
    filters[key as keyof AccountFilters] !== ""
  ).length

  const handlePriceChange = (value: number[]) => {
    setPriceRange(value)
    updateFilters({ priceMin: value[0], priceMax: value[1] })
  }

  return (
    <div className="space-y-4">
      {/* Mobile Filter Toggle */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => setShowFilters(!showFilters)}
          className="lg:hidden"
        >
          <Filter className="w-4 h-4 mr-2" />
          {language === "en" ? "Filters" : "المرشحات"}
          {activeFiltersCount > 0 && (
            <Badge variant="secondary" className="ml-2">
              {activeFiltersCount}
            </Badge>
          )}
        </Button>

        {activeFiltersCount > 0 && (
          <Button variant="ghost" size="sm" onClick={clearFilters}>
            <X className="w-4 h-4 mr-1" />
            {language === "en" ? "Clear" : "مسح"}
          </Button>
        )}
      </div>

      {/* Compact Filter Bar */}
      <div className={`${showFilters ? 'block' : 'hidden'} lg:block`}>
        <div className="bg-background border border-border rounded-lg p-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">

            {/* Status Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">
                {language === "en" ? "Status" : "الحالة"}
              </label>
              <Select
                value={filters.status || ""}
                onValueChange={(value) =>
                  updateFilters({ status: value as any || undefined })
                }
              >
                <SelectTrigger className="h-9">
                  <SelectValue placeholder={language === "en" ? "All Status" : "جميع الحالات"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">
                    {language === "en" ? "All Status" : "جميع الحالات"}
                  </SelectItem>
                  <SelectItem value="active">
                    {language === "en" ? "Active" : "نشط"}
                  </SelectItem>
                  <SelectItem value="inactive">
                    {language === "en" ? "Inactive" : "غير نشط"}
                  </SelectItem>
                  <SelectItem value="sold">
                    {language === "en" ? "Sold" : "مباع"}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Featured Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">
                {language === "en" ? "Featured" : "مميز"}
              </label>
              <Select
                value={filters.isFeatured !== undefined ? filters.isFeatured.toString() : ""}
                onValueChange={(value) =>
                  updateFilters({
                    isFeatured: value === "" ? undefined : value === "true"
                  })
                }
              >
                <SelectTrigger className="h-9">
                  <SelectValue placeholder={language === "en" ? "All Accounts" : "جميع الحسابات"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">
                    {language === "en" ? "All Accounts" : "جميع الحسابات"}
                  </SelectItem>
                  <SelectItem value="true">
                    <div className="flex items-center">
                      <Star className="w-4 h-4 mr-2" />
                      {language === "en" ? "Featured Only" : "المميزة فقط"}
                    </div>
                  </SelectItem>
                  <SelectItem value="false">
                    {language === "en" ? "Regular" : "عادية"}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Price Range */}
            <div className="space-y-2 sm:col-span-2">
              <label className="text-sm font-medium">
                {language === "en" ? "Price Range" : "نطاق السعر"}
              </label>
              <div className="px-2">
                <Slider
                  value={priceRange}
                  onValueChange={handlePriceChange}
                  max={500}
                  step={10}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground mt-1">
                  <span>${priceRange[0]}</span>
                  <span>${priceRange[1]}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap gap-2">
          {filters.status && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {filters.status}
              <X
                className="w-3 h-3 cursor-pointer"
                onClick={() => updateFilters({ status: undefined })}
              />
            </Badge>
          )}
          {filters.isFeatured !== undefined && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {filters.isFeatured ?
                (language === "en" ? "Featured" : "مميز") :
                (language === "en" ? "Regular" : "عادي")
              }
              <X
                className="w-3 h-3 cursor-pointer"
                onClick={() => updateFilters({ isFeatured: undefined })}
              />
            </Badge>
          )}
          {(filters.priceMin !== undefined || filters.priceMax !== undefined) && (
            <Badge variant="secondary" className="flex items-center gap-1">
              <DollarSign className="w-3 h-3" />
              ${filters.priceMin || 0} - ${filters.priceMax || 500}
              <X
                className="w-3 h-3 cursor-pointer"
                onClick={() => updateFilters({ priceMin: undefined, priceMax: undefined })}
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  )
}
