"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import Image from "next/image"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Calendar, Package, Search, Filter, Download, RefreshCw, CreditCard } from "lucide-react"
import { useLanguage } from "@/components/providers/language-provider"
import { Order, OrderStatus, PaymentStatus, getOrderStatusColor, getPaymentStatusColor } from "@/types/order"

interface UserOrdersProps {
  userId: string
}

export function UserOrders({ userId }: UserOrdersProps) {
  const { language } = useLanguage()
  
  // ## Database: Replace with actual API call to fetch user orders
  const [orders, setOrders] = useState<Order[]>([
    {
      id: "1",
      orderNumber: "ORD-240115-ABC123",
      userId: "user1",
      userEmail: "<EMAIL>",
      userName: "John Doe",
      status: "completed",
      paymentStatus: "paid",
      paymentMethod: "credit_card",
      subtotal: 49.99,
      taxAmount: 0,
      discountAmount: 0,
      totalAmount: 49.99,
      currency: "USD",
      paymentTransactionId: "txn_123456789",
      createdAt: "2024-01-15T10:30:00Z",
      updatedAt: "2024-01-15T11:00:00Z",
      completedAt: "2024-01-15T11:00:00Z",
      items: [
        {
          id: "item1",
          orderId: "1",
          productType: "hack",
          productId: "hack1",
          productName: {
            en: "PUBG Mobile Aimbot Pro",
            ar: "ايمبوت PUBG موبايل برو"
          },
          productDescription: {
            en: "Advanced aimbot with customizable settings",
            ar: "ايمبوت متقدم مع إعدادات قابلة للتخصيص"
          },
          unitPrice: 49.99,
          quantity: 1,
          totalPrice: 49.99,
          productImageUrl: "/placeholder.svg",
          createdAt: "2024-01-15T10:30:00Z"
        }
      ]
    },
    {
      id: "2",
      orderNumber: "ORD-240110-DEF456",
      userId: "user1",
      userEmail: "<EMAIL>",
      userName: "John Doe",
      status: "pending",
      paymentStatus: "pending",
      paymentMethod: "paypal",
      subtotal: 299.99,
      taxAmount: 0,
      discountAmount: 0,
      totalAmount: 299.99,
      currency: "USD",
      createdAt: "2024-01-10T14:20:00Z",
      updatedAt: "2024-01-10T14:20:00Z",
      items: [
        {
          id: "item2",
          orderId: "2",
          productType: "account",
          productId: "account1",
          productName: {
            en: "Premium PUBG Mobile Account",
            ar: "حساب PUBG موبايل مميز"
          },
          productDescription: {
            en: "High-level premium account with rare skins",
            ar: "حساب مميز عالي المستوى مع اسكنات نادرة"
          },
          unitPrice: 299.99,
          quantity: 1,
          totalPrice: 299.99,
          productImageUrl: "/placeholder.svg",
          createdAt: "2024-01-10T14:20:00Z"
        }
      ]
    }
  ])

  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<OrderStatus | "all">("all")
  const [sortBy, setSortBy] = useState<"date" | "amount">("date")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")

  // Filter and sort orders
  const filteredOrders = orders
    .filter(order => {
      const matchesSearch = order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           order.items.some(item => 
                             item.productName[language].toLowerCase().includes(searchTerm.toLowerCase())
                           )
      const matchesStatus = statusFilter === "all" || order.status === statusFilter
      return matchesSearch && matchesStatus
    })
    .sort((a, b) => {
      const multiplier = sortOrder === "asc" ? 1 : -1
      if (sortBy === "date") {
        return multiplier * (new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())
      } else {
        return multiplier * (a.totalAmount - b.totalAmount)
      }
    })

  const handleReorder = (orderId: string) => {
    // ## Database: Implement reorder functionality
    console.log("Reorder:", orderId)
  }

  const handleDownload = (orderId: string) => {
    // ## Database: Implement order receipt download
    console.log("Download receipt:", orderId)
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold">
            {language === "en" ? "Order History" : "تاريخ الطلبات"}
          </h2>
          <p className="text-muted-foreground">
            {language === "en" 
              ? `${filteredOrders.length} orders found` 
              : `تم العثور على ${filteredOrders.length} طلب`}
          </p>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder={language === "en" ? "Search orders..." : "البحث في الطلبات..."}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Status Filter */}
            <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as OrderStatus | "all")}>
              <SelectTrigger>
                <SelectValue placeholder={language === "en" ? "All Status" : "جميع الحالات"} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  {language === "en" ? "All Status" : "جميع الحالات"}
                </SelectItem>
                <SelectItem value="pending">
                  {language === "en" ? "Pending" : "قيد الانتظار"}
                </SelectItem>
                <SelectItem value="processing">
                  {language === "en" ? "Processing" : "قيد المعالجة"}
                </SelectItem>
                <SelectItem value="completed">
                  {language === "en" ? "Completed" : "مكتمل"}
                </SelectItem>
                <SelectItem value="cancelled">
                  {language === "en" ? "Cancelled" : "ملغي"}
                </SelectItem>
                <SelectItem value="refunded">
                  {language === "en" ? "Refunded" : "مسترد"}
                </SelectItem>
              </SelectContent>
            </Select>

            {/* Sort By */}
            <Select value={sortBy} onValueChange={(value) => setSortBy(value as "date" | "amount")}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date">
                  {language === "en" ? "Sort by Date" : "ترتيب حسب التاريخ"}
                </SelectItem>
                <SelectItem value="amount">
                  {language === "en" ? "Sort by Amount" : "ترتيب حسب المبلغ"}
                </SelectItem>
              </SelectContent>
            </Select>

            {/* Sort Order */}
            <Select value={sortOrder} onValueChange={(value) => setSortOrder(value as "asc" | "desc")}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="desc">
                  {language === "en" ? "Newest First" : "الأحدث أولاً"}
                </SelectItem>
                <SelectItem value="asc">
                  {language === "en" ? "Oldest First" : "الأقدم أولاً"}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Orders List */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="space-y-4"
      >
        {filteredOrders.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <Package className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {language === "en" ? "No orders found" : "لم يتم العثور على طلبات"}
              </h3>
              <p className="text-muted-foreground">
                {language === "en" 
                  ? "You haven't placed any orders yet or no orders match your search criteria."
                  : "لم تقم بوضع أي طلبات بعد أو لا توجد طلبات تطابق معايير البحث الخاصة بك."}
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredOrders.map((order) => (
            <motion.div key={order.id} variants={itemVariants}>
              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-4">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div>
                      <CardTitle className="text-lg">
                        {language === "en" ? "Order" : "طلب"} #{order.orderNumber}
                      </CardTitle>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground mt-1">
                        <Calendar className="w-4 h-4" />
                        {new Date(order.createdAt).toLocaleDateString()}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={`${getOrderStatusColor(order.status)} text-white`}>
                        {order.status}
                      </Badge>
                      <Badge className={`${getPaymentStatusColor(order.paymentStatus)} text-white`}>
                        {order.paymentStatus}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* Order Items */}
                  <div className="space-y-3 mb-4">
                    {order.items.map((item) => (
                      <div key={item.id} className="flex items-center gap-4 p-3 bg-muted/50 rounded-lg">
                        <Image
                          src={item.productImageUrl || "/placeholder.svg"}
                          alt={item.productName[language]}
                          width={60}
                          height={60}
                          className="rounded-lg object-cover"
                        />
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium truncate">
                            {item.productName[language]}
                          </h4>
                          <p className="text-sm text-muted-foreground truncate">
                            {item.productDescription[language]}
                          </p>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {item.productType}
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              {language === "en" ? "Qty:" : "الكمية:"} {item.quantity}
                            </span>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-semibold">${item.totalPrice}</div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <Separator className="my-4" />

                  {/* Order Summary */}
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 text-sm">
                        <CreditCard className="w-4 h-4" />
                        <span className="capitalize">{order.paymentMethod.replace('_', ' ')}</span>
                      </div>
                      {order.paymentTransactionId && (
                        <div className="text-xs text-muted-foreground">
                          {language === "en" ? "Transaction:" : "المعاملة:"} {order.paymentTransactionId}
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-4">
                      <div className="text-right">
                        <div className="text-sm text-muted-foreground">
                          {language === "en" ? "Total" : "المجموع"}
                        </div>
                        <div className="text-xl font-bold text-orange-400">
                          ${order.totalAmount}
                        </div>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDownload(order.id)}
                        >
                          <Download className="w-4 h-4" />
                        </Button>
                        {order.status === "completed" && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleReorder(order.id)}
                          >
                            <RefreshCw className="w-4 h-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))
        )}
      </motion.div>
    </div>
  )
}
