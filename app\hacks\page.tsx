import type { Metadata } from "next"
import { HacksGrid } from "@/components/hacks/hacks-grid"
import { HacksFilters } from "@/components/hacks/hacks-filters"
import { PageHeader } from "@/components/ui/page-header"
import { Hack } from "@/types/hack"

export const metadata: Metadata = {
  title: "PUBG Hacks - Professional Gaming Hacks | PUBG Store",
  description:
    "Advanced PUBG gaming hacks and utilities. Safe, undetected, and regularly updated. Enhance your gaming experience today.",
  openGraph: {
    title: "PUBG Hacks - Professional Gaming Hacks",
    description: "Advanced PUBG gaming hacks and utilities. Safe, undetected, and regularly updated.",
    url: "https://pubgstore.com/hacks",
  },
}

// ## TODO: Fetch hacks from Supabase: 'hacks' table with filters
const getHacks = async (searchParams: any) => {
  // Placeholder data - replace with Supabase query using new Hack structure
  return [
    {
      id: "1",
      name: {
        en: "PUBG Mobile Aimbot Pro",
        ar: "ايمبوت PUBG موبايل برو"
      },
      type: "mobile" as const,
      platform: "android" as const,
      rootStatus: "root" as const,
      price: 49.99,
      company: "GameHacks Inc",
      version: "2.1.5",
      description: {
        en: "Advanced aimbot with customizable settings for PUBG Mobile. Features smooth aiming, target prediction, and anti-detection technology.",
        ar: "ايمبوت متقدم مع إعدادات قابلة للتخصيص لـ PUBG موبايل. يتميز بالتصويب السلس وتوقع الهدف وتقنية مكافحة الكشف."
      },
      status: "active" as const,
      createdAt: "2024-01-15T10:30:00Z",
      updatedAt: "2024-01-20T14:45:00Z",
      imageUrl: "/placeholder.svg?height=300&width=400",
      isFeatured: true,
      downloadCount: 1250,
      lastUpdated: "2024-01-20"
    },
    {
      id: "2",
      name: {
        en: "iOS PUBG ESP Hack",
        ar: "هاك ESP لـ PUBG على iOS"
      },
      type: "mobile" as const,
      platform: "ios" as const,
      jailbreakStatus: "jailbroken" as const,
      price: 39.99,
      company: "MobileHacks Ltd",
      version: "1.8.2",
      description: {
        en: "ESP wallhack for iOS devices with jailbreak. See enemies through walls, get distance information, and health status.",
        ar: "هاك ESP للأجهزة iOS مع الجيلبريك. شاهد الأعداء من خلال الجدران واحصل على معلومات المسافة وحالة الصحة."
      },
      status: "active" as const,
      createdAt: "2024-01-10T09:15:00Z",
      updatedAt: "2024-01-18T11:30:00Z",
      imageUrl: "/placeholder.svg?height=300&width=400",
      isFeatured: false,
      downloadCount: 890,
      lastUpdated: "2024-01-18"
    },
    {
      id: "3",
      name: {
        en: "PUBG PC Ultimate Hack",
        ar: "هاك PUBG PC النهائي"
      },
      type: "pc" as const,
      price: 79.99,
      company: "PCGaming Tools",
      version: "3.0.1",
      description: {
        en: "Complete hack suite for PUBG PC with aimbot, ESP, radar, and more. Professional-grade features for competitive gaming.",
        ar: "مجموعة هاكات كاملة لـ PUBG PC مع ايمبوت و ESP ورادار والمزيد. ميزات احترافية للألعاب التنافسية."
      },
      status: "active" as const,
      createdAt: "2024-01-05T16:20:00Z",
      updatedAt: "2024-01-22T10:15:00Z",
      imageUrl: "/placeholder.svg?height=300&width=400",
      isFeatured: true,
      downloadCount: 2100,
      lastUpdated: "2024-01-22"
    },
  ]
}

export default async function HacksPage({
  searchParams,
}: {
  searchParams: { [key: string]: string | string[] | undefined }
}) {
  const hacks = await getHacks(searchParams)

  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    name: "PUBG Gaming Hacks",
    description: "Professional PUBG gaming hacks and utilities",
    url: "https://pubgstore.com/hacks",
    mainEntity: {
      "@type": "ItemList",
      itemListElement: hacks.map((hack, index) => ({
        "@type": "SoftwareApplication",
        position: index + 1,
        name: hack.name.en,
        description: hack.description.en,
        operatingSystem: hack.type === "pc" ? "Windows" : hack.platform === "android" ? "Android" : "iOS",
        offers: {
          "@type": "Offer",
          price: hack.price,
          priceCurrency: "USD",
          availability: hack.status === "active" ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
        },
      })),
    },
  }

  return (
    <>
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }} />
      <div className="min-h-screen bg-zinc-900">
        <div className="container mx-auto px-4 py-8">
          <PageHeader
            title="Gaming Hacks"
            description="Professional hacks and utilities to enhance your PUBG experience"
          />
          <div className="flex flex-col lg:flex-row gap-8">
            <aside className="lg:w-1/4">
              <HacksFilters />
            </aside>
            <main className="lg:w-3/4">
              <HacksGrid hacks={hacks} />
            </main>
          </div>
        </div>
      </div>
    </>
  )
}
