"use client"

import { motion } from "framer-motion"
import Link from "next/link"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardFooter, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Star, ArrowRight } from "lucide-react"
import { useLanguage } from "@/components/providers/language-provider"

interface Product {
  id: string
  title: string
  price: number
  image: string
  rating: number
  features: string[]
}

interface FeaturedProductsProps {
  products: {
    accounts: Product[]
    hacks: Product[]
  }
}

export function FeaturedProducts({ products }: FeaturedProductsProps) {
  const { language, t } = useLanguage()

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  }

  return (
    <section className="py-20 bg-zinc-900">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            {language === "en" ? "Featured Products" : "المنتجات المميزة"}
          </h2>
          <p className="text-zinc-400 text-lg max-w-2xl mx-auto">
            {language === "en"
              ? "Discover our most popular PUBG accounts and gaming hacks"
              : "اكتشف أشهر حسابات PUBG وهاكات الألعاب لدينا"}
          </p>
        </motion.div>

        {/* Featured Accounts */}
        <div className="mb-16">
          <div className="flex items-center justify-between mb-8">
            <h3 className="text-2xl font-bold text-white">
              {language === "en" ? "Premium Accounts" : "الحسابات المميزة"}
            </h3>
            <Link href="/accounts">
              <Button
                variant="outline"
                className="border-orange-400 text-orange-400 hover:bg-orange-400 hover:text-white bg-transparent"
              >
                {language === "en" ? "View All" : "عرض الكل"}
                <ArrowRight className="ml-2 w-4 h-4" />
              </Button>
            </Link>
          </div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {products.accounts.map((account) => (
              <motion.div key={account.id} variants={itemVariants}>
                <Card className="bg-black border-zinc-700 hover:border-orange-400 transition-colors group">
                  <CardHeader className="p-0">
                    <div className="relative overflow-hidden rounded-t-lg">
                      <Image
                        src={account.image || "/placeholder.svg"}
                        alt={account.title}
                        width={400}
                        height={300}
                        className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <Badge className="absolute top-4 left-4 bg-orange-500 text-white">
                        {language === "en" ? "Featured" : "مميز"}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="p-6">
                    <h4 className="text-xl font-semibold text-white mb-2">{account.title}</h4>
                    <div className="flex items-center space-x-2 mb-4">
                      <div className="flex items-center">
                        <Star className="w-4 h-4 fill-orange-400 text-orange-400" />
                        <span className="text-zinc-300 text-sm ml-1">{account.rating}</span>
                      </div>
                      <span className="text-zinc-500">•</span>
                      <span className="text-zinc-400 text-sm">
                        {language === "en" ? "Premium Account" : "حساب مميز"}
                      </span>
                    </div>
                    <ul className="space-y-1 mb-4">
                      {account.features.slice(0, 3).map((feature, index) => (
                        <li key={index} className="text-zinc-400 text-sm flex items-center">
                          <span className="w-1.5 h-1.5 bg-orange-400 rounded-full mr-2" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                  <CardFooter className="p-6 pt-0">
                    <div className="flex items-center justify-between w-full">
                      <span className="text-2xl font-bold text-orange-400">${account.price}</span>
                      <Button className="bg-orange-500 hover:bg-orange-600 text-white">
                        {language === "en" ? "View Details" : "عرض التفاصيل"}
                      </Button>
                    </div>
                  </CardFooter>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Featured Hacks */}
        <div>
          <div className="flex items-center justify-between mb-8">
            <h3 className="text-2xl font-bold text-white">{language === "en" ? "Gaming Hacks" : "هاكات الألعاب"}</h3>
            <Link href="/hacks">
              <Button
                variant="outline"
                className="border-orange-400 text-orange-400 hover:bg-orange-400 hover:text-white bg-transparent"
              >
                {language === "en" ? "View All" : "عرض الكل"}
                <ArrowRight className="ml-2 w-4 h-4" />
              </Button>
            </Link>
          </div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {products.hacks.map((hack) => (
              <motion.div key={hack.id} variants={itemVariants}>
                <Card className="bg-black border-zinc-700 hover:border-orange-400 transition-colors group">
                  <CardHeader className="p-0">
                    <div className="relative overflow-hidden rounded-t-lg">
                      <Image
                        src={hack.image || "/placeholder.svg"}
                        alt={hack.title}
                        width={400}
                        height={300}
                        className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <Badge className="absolute top-4 left-4 bg-green-500 text-white">
                        {language === "en" ? "Safe" : "آمن"}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="p-6">
                    <h4 className="text-xl font-semibold text-white mb-2">{hack.title}</h4>
                    <div className="flex items-center space-x-2 mb-4">
                      <div className="flex items-center">
                        <Star className="w-4 h-4 fill-orange-400 text-orange-400" />
                        <span className="text-zinc-300 text-sm ml-1">{hack.rating}</span>
                      </div>
                      <span className="text-zinc-500">•</span>
                      <span className="text-zinc-400 text-sm">{language === "en" ? "Gaming Tool" : "أداة ألعاب"}</span>
                    </div>
                    <ul className="space-y-1 mb-4">
                      {hack.features.slice(0, 3).map((feature, index) => (
                        <li key={index} className="text-zinc-400 text-sm flex items-center">
                          <span className="w-1.5 h-1.5 bg-orange-400 rounded-full mr-2" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                  <CardFooter className="p-6 pt-0">
                    <div className="flex items-center justify-between w-full">
                      <span className="text-2xl font-bold text-orange-400">${hack.price}</span>
                      <Button className="bg-orange-500 hover:bg-orange-600 text-white">
                        {language === "en" ? "View Details" : "عرض التفاصيل"}
                      </Button>
                    </div>
                  </CardFooter>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  )
}
